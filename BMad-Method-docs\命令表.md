# BMad-Method 命令表与使用样例

## 📋 文档信息

**生成时间：** 2025年8月1日  
**BMad版本：** v4.33.1  
**适用环境：** Augment Code集成环境  
**文档类型：** 命令参考与实战样例  

## 🚀 系统级命令

### 环境管理命令
```bash
# 验证安装和配置
python .augment/validate_config.py

# 查看系统状态
python .augment/bmad_augment.py status

# 列出所有可用代理
python .augment/bmad_augment.py list

# 进入交互模式
python .augment/bmad_augment.py interactive

# 直接调用代理
python .augment/bmad_augment.py agent "@bmad-master"

# 生成代理激活提示词
python .augment/call_agent.py bmad-master "请帮我创建项目文档"
```

## 🤖 代理调用方式

### 在Augment Code中的调用方式
```bash
# 方式1：直接激活代理
@bmad-master

# 方式2：激活代理并执行命令
@dev *develop-story

# 方式3：带参数的命令
@pm *research "移动支付市场分析"
```

### 命令行调用方式
```bash
# 直接调用
python .augment/call_agent.py dev

# 带用户请求调用
python .augment/call_agent.py pm "需要创建用户登录功能的PRD"

# 交互模式调用
python .augment/bmad_augment.py interactive
# 然后输入：@bmad-master
```

## 📖 通用命令（所有代理支持）

### `*help` - 显示帮助信息
- **用法：** `*help`
- **描述：** 显示代理特定的帮助信息和命令列表
- **样例：** `@dev *help`

### `*exit` - 退出代理模式
- **用法：** `*exit`
- **描述：** 退出当前代理模式，返回普通对话
- **样例：** `@pm *exit`

## 🧙 BMad Master 命令详解

**调用方式：** `@bmad-master`

### `*help` - 显示所有可用命令
- **用法：** `*help`
- **描述：** 显示所有可用命令的编号列表，方便快速选择
- **样例：** `@bmad-master *help`

### `*kb` - 切换知识库模式
- **用法：** `*kb`
- **描述：** 切换知识库模式（默认关闭），开启后将加载并引用BMad知识库
- **样例：** `@bmad-master *kb`

### `*task` - 执行指定任务
- **用法：** `*task {任务名称}`
- **描述：** 执行指定任务，如果未指定任务名称，将显示所有可用任务列表
- **样例：** `@bmad-master *task create-doc`

### `*create-doc` - 使用模板创建文档
- **用法：** `*create-doc {模板名称}`
- **描述：** 使用指定模板创建文档，如果未指定模板，将显示所有可用模板列表
- **样例：** `@bmad-master *create-doc prd-tmpl`

### `*doc-out` - 输出完整文档
- **用法：** `*doc-out`
- **描述：** 将完整文档输出到当前目标文件
- **样例：** `@bmad-master *doc-out`

### `*document-project` - 项目文档化
- **用法：** `*document-project`
- **描述：** 执行项目文档化任务，分析项目结构并生成相关文档
- **样例：** `@bmad-master *document-project`

### `*execute-checklist` - 运行检查清单
- **用法：** `*execute-checklist {清单名称}`
- **描述：** 运行指定检查清单，如果未指定清单，将显示所有可用检查清单
- **样例：** `@bmad-master *execute-checklist pm-checklist`

### `*shard-doc` - 文档分片
- **用法：** `*shard-doc {文档} {目标位置}`
- **描述：** 将大文档分片到指定目标位置，便于管理和维护
- **样例：** `@bmad-master *shard-doc prd.md docs/prd/`

### `*yolo` - 切换Yolo模式
- **用法：** `*yolo`
- **描述：** 切换Yolo模式，启用快速执行模式
- **样例：** `@bmad-master *yolo`

### BMad Master 实战样例

#### 样例1：项目初始化文档化
```bash
# 激活BMad Master
@bmad-master

# 执行项目文档化
*document-project

# 预期输出：
# 正在分析项目结构...
# 生成项目概览文档...
# 创建技术债务报告...
# 文档已保存到 docs/project-overview.md
```

#### 样例2：创建PRD文档
```bash
@bmad-master
*create-doc prd-tmpl

# 系统会引导您填写：
# 1. 项目名称
# 2. 目标用户
# 3. 核心功能
# 4. 成功指标
```

## 💻 开发者 (Dev) 命令详解

**调用方式：** `@dev`

### `*help` - 显示开发者命令
- **用法：** `*help`
- **描述：** 显示所有可用的开发者专用命令列表
- **样例：** `@dev *help`

### `*develop-story` - 开发用户故事
- **用法：** `*develop-story`
- **描述：** 开发用户故事，执行完整的开发流程：读取任务→实现功能→编写测试→执行验证→更新状态
- **样例：** `@dev *develop-story`

### `*run-tests` - 执行代码检查和测试
- **用法：** `*run-tests`
- **描述：** 运行代码检查（linting）和自动化测试，确保代码质量
- **样例：** `@dev *run-tests`

### `*explain` - 详细解释操作
- **用法：** `*explain`
- **描述：** 详细解释刚才的操作过程，用于学习和培训，以培训初级工程师的方式说明
- **样例：** `@dev *explain`

### 开发者实战样例

#### 样例1：开发用户登录功能
```bash
# 激活开发者代理
@dev

# 开始开发故事
*develop-story

# 执行流程：
# 1. 读取story-001.md中的任务
# 2. 实现用户登录API
# 3. 编写单元测试
# 4. 执行测试验证
# 5. 更新任务状态为完成
# 6. 更新文件列表
```

#### 样例2：代码审查和重构
```bash
@dev
*run-tests

# 输出示例：
# 运行ESLint检查...
# 运行Jest单元测试...
# 测试覆盖率: 85%
# 发现3个代码质量问题
```

## 📋 产品经理 (PM) 命令详解

**调用方式：** `@pm`

### `*help` - 显示PM命令
- **用法：** `*help`
- **描述：** 显示所有可用的产品经理专用命令列表
- **样例：** `@pm *help`

### `*create` - 创建产品需求文档
- **用法：** `*create`
- **描述：** 创建新的产品需求文档（PRD），使用create-doc任务和prd-tmpl模板
- **样例：** `@pm *create`

### `*create-brownfield-prd` - 创建棕地项目PRD
- **用法：** `*create-brownfield-prd`
- **描述：** 为现有项目（棕地项目）创建PRD，使用brownfield-prd-tmpl模板
- **样例：** `@pm *create-brownfield-prd`

### `*research` - 进行深度研究
- **用法：** `*research {主题}`
- **描述：** 对指定主题进行深度研究，执行create-deep-research-prompt任务
- **样例：** `@pm *research "电商支付系统"`

### `*create-epic` - 创建史诗
- **用法：** `*create-epic`
- **描述：** 为棕地项目创建史诗，执行brownfield-create-epic任务
- **样例：** `@pm *create-epic`

### `*create-story` - 创建用户故事
- **用法：** `*create-story`
- **描述：** 从需求创建用户故事，执行brownfield-create-story任务
- **样例：** `@pm *create-story`

### `*doc-out` - 输出文档
- **用法：** `*doc-out`
- **描述：** 将完整的PRD文档输出到当前目标文件
- **样例：** `@pm *doc-out`

### `*shard-prd` - PRD分片
- **用法：** `*shard-prd`
- **描述：** 对提供的prd.md运行分片任务，如果未找到prd.md会询问路径
- **样例：** `@pm *shard-prd`

### 产品经理实战样例

#### 样例1：创建电商应用PRD
```bash
@pm
*create

# 交互式创建过程：
# 项目名称: 智能电商平台
# 目标用户: 18-35岁都市消费者
# 核心功能: 商品浏览、购物车、支付、订单管理
# 商业目标: 提升用户购买转化率30%
```

#### 样例2：市场研究
```bash
@pm
*research "移动支付市场趋势分析"

# 输出：
# 正在生成深度研究提示词...
# 研究范围：移动支付技术发展、用户行为、竞争格局
# 建议研究方法：用户调研、竞品分析、数据分析
```

## 🏗️ 架构师 (Architect) 命令详解

**调用方式：** `@architect`

### `*help` - 显示架构师命令
- **用法：** `*help`
- **描述：** 显示所有可用的架构师专用命令列表
- **样例：** `@architect *help`

### `*create-full-stack-architecture` - 创建全栈架构文档
- **用法：** `*create-full-stack-architecture`
- **描述：** 创建全栈架构文档，使用fullstack-architecture-tmpl.yaml模板
- **样例：** `@architect *create-full-stack-architecture`

### `*create-backend-architecture` - 创建后端架构文档
- **用法：** `*create-backend-architecture`
- **描述：** 创建后端架构文档，使用architecture-tmpl.yaml模板
- **样例：** `@architect *create-backend-architecture`

### `*create-front-end-architecture` - 创建前端架构文档
- **用法：** `*create-front-end-architecture`
- **描述：** 创建前端架构文档，使用front-end-architecture-tmpl.yaml模板
- **样例：** `@architect *create-front-end-architecture`

### `*create-brownfield-architecture` - 创建棕地项目架构文档
- **用法：** `*create-brownfield-architecture`
- **描述：** 创建棕地项目架构文档，使用brownfield-architecture-tmpl.yaml模板
- **样例：** `@architect *create-brownfield-architecture`

### `*research` - 进行技术研究
- **用法：** `*research {主题}`
- **描述：** 对指定技术主题进行深度研究，执行create-deep-research-prompt任务
- **样例：** `@architect *research "微服务架构"`

### `*execute-checklist` - 执行架构检查清单
- **用法：** `*execute-checklist {清单名称}`
- **描述：** 运行架构相关的检查清单，默认运行architect-checklist
- **样例：** `@architect *execute-checklist`

### 架构师实战样例

#### 样例1：设计微服务架构
```bash
@architect
*create-full-stack-architecture

# 引导设计过程：
# 1. 技术栈选择：Node.js + React + MongoDB
# 2. 架构模式：微服务 + API网关
# 3. 数据库设计：分库分表策略
# 4. 缓存策略：Redis集群
# 5. 部署方案：Docker + Kubernetes
```

#### 样例2：技术选型研究
```bash
@architect
*research "容器化部署最佳实践"

# 输出：
# 正在分析容器化技术栈...
# 推荐方案：Docker + Kubernetes + Helm
# 关键考虑因素：性能、安全性、可维护性
```

## 🔍 QA工程师 (QA) 命令详解

**调用方式：** `@qa`

### `*help` - 显示QA命令
- **用法：** `*help`
- **描述：** 显示所有可用的QA工程师专用命令列表
- **样例：** `@qa *help`

### `*review` - 审查用户故事
- **用法：** `*review {故事名称}`
- **描述：** 审查指定的用户故事，执行review-story任务。如果未指定故事，会审查docs/stories中的最高序号故事
- **样例：** `@qa *review story-001`
- **注意：** QA代理在审查故事时只能更新"QA Results"部分，不能修改其他部分

### QA工程师实战样例

#### 样例1：代码审查
```bash
@qa
*review

# 审查过程：
# 1. 检查代码质量和规范
# 2. 验证测试覆盖率
# 3. 检查安全漏洞
# 4. 更新QA Results部分
```

## 📈 业务分析师 (Analyst) 命令详解

**调用方式：** `@analyst`

### `*help` - 显示分析师命令
- **用法：** `*help`
- **描述：** 显示所有可用的业务分析师专用命令列表
- **样例：** `@analyst *help`

### `*create-project-brief` - 创建项目简介
- **用法：** `*create-project-brief`
- **描述：** 创建项目简介文档，使用project-brief-tmpl.yaml模板
- **样例：** `@analyst *create-project-brief`

### `*perform-market-research` - 执行市场研究
- **用法：** `*perform-market-research`
- **描述：** 执行市场研究分析，使用market-research-tmpl.yaml模板
- **样例：** `@analyst *perform-market-research`

### `*create-competitor-analysis` - 创建竞品分析
- **用法：** `*create-competitor-analysis`
- **描述：** 创建竞品分析报告，使用competitor-analysis-tmpl.yaml模板
- **样例：** `@analyst *create-competitor-analysis`

### `*brainstorm` - 促进头脑风暴会议
- **用法：** `*brainstorm {主题}`
- **描述：** 促进结构化头脑风暴会议，运行facilitate-brainstorming-session.md任务
- **样例：** `@analyst *brainstorm "产品创新点"`

### `*elicit` - 运行高级启发任务
- **用法：** `*elicit`
- **描述：** 运行高级需求启发任务，执行advanced-elicitation任务
- **样例：** `@analyst *elicit`

### 业务分析师实战样例

#### 样例1：竞品分析
```bash
@analyst
*create-competitor-analysis

# 分析维度：
# 1. 竞品功能对比
# 2. 用户体验评估
# 3. 商业模式分析
# 4. 技术架构对比
# 5. 市场定位分析
```

## 🎨 UX专家 (UX Expert) 命令详解

**调用方式：** `@ux-expert`

### `*help` - 显示UX命令
- **用法：** `*help`
- **描述：** 显示所有可用的UX专家专用命令列表
- **样例：** `@ux-expert *help`

### `*create-front-end-spec` - 创建前端规范文档
- **用法：** `*create-front-end-spec`
- **描述：** 创建前端规范文档，使用front-end-spec-tmpl.yaml模板
- **样例：** `@ux-expert *create-front-end-spec`

### `*generate-ui-prompt` - 生成AI UI提示词
- **用法：** `*generate-ui-prompt`
- **描述：** 生成AI UI设计提示词，运行generate-ai-frontend-prompt.md任务
- **样例：** `@ux-expert *generate-ui-prompt`

## 📊 产品负责人 (PO) 命令详解

**调用方式：** `@po`

### `*help` - 显示PO命令
- **用法：** `*help`
- **描述：** 显示所有可用的产品负责人专用命令列表
- **样例：** `@po *help`

### `*execute-checklist-po` - 运行PO检查清单
- **用法：** `*execute-checklist-po`
- **描述：** 运行产品负责人专用检查清单，执行po-master-checklist检查清单
- **样例：** `@po *execute-checklist-po`

### `*shard-doc` - 文档分片
- **用法：** `*shard-doc {文档} {目标}`
- **描述：** 将指定文档分片到目标位置，运行shard-doc任务
- **样例：** `@po *shard-doc requirements.md docs/requirements/`

### `*correct-course` - 执行纠偏任务
- **用法：** `*correct-course`
- **描述：** 执行项目纠偏任务，运行correct-course任务
- **样例：** `@po *correct-course`

### `*create-epic` - 创建史诗
- **用法：** `*create-epic`
- **描述：** 为棕地项目创建史诗，执行brownfield-create-epic任务
- **样例：** `@po *create-epic`

### `*create-story` - 创建用户故事
- **用法：** `*create-story`
- **描述：** 从需求创建用户故事，执行brownfield-create-story任务
- **样例：** `@po *create-story`

### `*doc-out` - 输出文档
- **用法：** `*doc-out`
- **描述：** 将完整文档输出到当前目标文件
- **样例：** `@po *doc-out`

## 🏃 Scrum Master (SM) 命令详解

**调用方式：** `@sm`

### `*help` - 显示SM命令
- **用法：** `*help`
- **描述：** 显示所有可用的Scrum Master专用命令列表
- **样例：** `@sm *help`

### `*draft` - 创建下一个故事
- **用法：** `*draft`
- **描述：** 执行创建下一个故事任务，运行create-next-story.md任务
- **样例：** `@sm *draft`

### `*correct-course` - 执行纠偏任务
- **用法：** `*correct-course`
- **描述：** 执行项目纠偏任务，运行correct-course.md任务
- **样例：** `@sm *correct-course`

### `*story-checklist` - 执行故事检查清单
- **用法：** `*story-checklist`
- **描述：** 执行故事检查清单，运行story-draft-checklist.md检查清单
- **样例：** `@sm *story-checklist`

### UX专家实战样例

#### 样例1：设计移动端界面
```bash
@ux-expert
*generate-ui-prompt

# 生成AI UI提示词：
# "创建一个现代化的移动端登录界面，包含邮箱输入、密码输入、
# 记住我选项和登录按钮，使用Material Design风格"
```

## 🔄 完整工作流样例

### 样例1：从零开始的项目开发流程

```bash
# 第1步：项目文档化
@bmad-master
*document-project

# 第2步：需求分析
@pm
*create

# 第3步：架构设计
@architect
*create-full-stack-architecture

# 第4步：用户故事创建
@sm
*draft

# 第5步：开发实现
@dev
*develop-story

# 第6步：质量审查
@qa
*review

# 第7步：文档输出
@bmad-master
*doc-out
```

### 样例2：棕地项目改进流程

```bash
# 第1步：现状分析
@bmad-master
*document-project

# 第2步：改进需求
@pm
*create-brownfield-prd

# 第3步：架构优化
@architect
*create-brownfield-architecture

# 第4步：实施计划
@sm
*draft

# 第5步：代码重构
@dev
*develop-story

# 第6步：质量保证
@qa
*review
```

## 🎯 使用技巧与最佳实践

### 命令使用技巧
1. **查看帮助优先**：每个代理激活后先使用 `*help`
2. **分步执行**：复杂任务分解为多个命令
3. **验证结果**：重要操作后检查输出
4. **保持上下文**：在同一会话中使用相关代理

### 代理协作模式
- **需求→设计→开发**：`@pm` → `@architect` → `@dev`
- **分析→规划→实施**：`@analyst` → `@sm` → `@dev`
- **设计→开发→测试**：`@ux-expert` → `@dev` → `@qa`

### 常用命令组合
```bash
# 快速PRD创建和分片
@pm *create && @pm *shard-prd

# 架构设计和验证
@architect *create-backend-architecture && @architect *execute-checklist

# 开发和测试
@dev *develop-story && @dev *run-tests
```

## 🔧 故障排除

### 常见问题解决
```bash
# 检查系统状态
python .augment/bmad_augment.py status

# 验证配置
python .augment/validate_config.py

# 查看日志
tail -f .ai/augment-debug.log

# 重新加载代理
@bmad-master *help
```

### 错误处理
- **代理未找到**：检查代理名称拼写
- **命令无效**：确认命令前缀 `*`
- **权限问题**：检查文件路径和权限
- **资源加载失败**：验证 `.bmad-core` 目录完整性

## 📚 高级使用样例

### 复杂项目管理样例

#### 样例：电商平台开发完整流程
```bash
# 阶段1：项目启动和需求分析
@bmad-master
*document-project

@analyst
*create-project-brief
*perform-market-research
*create-competitor-analysis

@pm
*create
*research "电商用户行为分析"

# 阶段2：架构设计和技术选型
@architect
*create-full-stack-architecture
*research "高并发电商架构"
*execute-checklist

# 阶段3：用户体验设计
@ux-expert
*create-front-end-spec
*generate-ui-prompt

# 阶段4：敏捷开发管理
@sm
*draft
*story-checklist

# 阶段5：开发实施
@dev
*develop-story
*run-tests
*explain

# 阶段6：质量保证
@qa
*review

# 阶段7：文档整理和发布
@bmad-master
*doc-out
*shard-doc
```

### 团队协作样例

#### 样例：多角色协作开发用户认证模块
```bash
# PM定义需求
@pm
*create-story

# 架构师设计方案
@architect
*create-backend-architecture

# UX设计界面
@ux-expert
*create-front-end-spec

# 开发者实现功能
@dev
*develop-story

# QA审查质量
@qa
*review

# SM跟踪进度
@sm
*story-checklist
```

## 🎨 自定义命令样例

### 创建自定义任务
```yaml
# 在 .bmad-core/tasks/ 目录下创建 custom-task.md
name: "自定义数据分析任务"
description: "执行特定的数据分析流程"
steps:
  - "收集数据源"
  - "清洗和预处理"
  - "统计分析"
  - "生成报告"
```

### 创建自定义模板
```yaml
# 在 .bmad-core/templates/ 目录下创建 custom-template.yaml
name: "API设计文档模板"
sections:
  - title: "API概述"
    content: "描述API的主要功能和用途"
  - title: "接口列表"
    content: "详细的接口定义和参数说明"
  - title: "错误码"
    content: "错误处理和状态码定义"
```

## 🔍 调试和监控

### 启用详细日志
```bash
# 设置环境变量启用调试模式
export BMAD_DEBUG=true

# 查看详细执行日志
tail -f .ai/augment-debug.log

# 监控代理执行状态
python .augment/bmad_augment.py monitor
```

### 性能监控
```bash
# 查看代理执行时间
@bmad-master
*task performance-monitor

# 分析任务执行效率
@analyst
*brainstorm "工作流优化"
```

## 📊 统计信息

### 命令使用统计
- **总代理数量：** 9个核心代理
- **总命令数量：** 57个专用命令
- **最常用命令：** `*help`, `*create`, `*develop-story`
- **最复杂工作流：** 完整项目开发流程（7个阶段）

### 代理功能覆盖
- **需求管理：** PM, PO, Analyst
- **技术设计：** Architect, UX Expert
- **开发实施：** Dev, QA
- **项目管理：** SM, BMad Master

---

**文档维护者：** BMad Master
**最后更新：** 2025年8月1日
**版本：** v4.33.1
**适用环境：** Augment Code集成环境
**文档状态：** 完整版本，包含所有命令和实战样例
